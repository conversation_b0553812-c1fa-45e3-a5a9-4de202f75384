require 'rails_helper'

RSpec.describe UserLeaderPhoto, type: :model do
  describe 'associations' do
    let(:user_leader_photo) { create(:user_leader_photo) }

    it 'belongs to creator' do
      expect(user_leader_photo.creator).to be_present
    end

    it 'belongs to user_poster_layout' do
      expect(user_leader_photo.user_poster_layout).to be_present
    end

    it 'can have optional photo' do
      user_leader_photo.photo = nil
      expect(user_leader_photo).to be_valid
    end

    it 'can have optional user' do
      user_leader_photo.user = nil
      expect(user_leader_photo).to be_valid
    end

    it 'can have optional circle' do
      user_leader_photo.circle = nil
      expect(user_leader_photo).to be_valid
    end
  end

  describe 'validations' do
    let(:user_leader_photo) { build(:user_leader_photo) }

    it 'validates presence of header_type' do
      user_leader_photo.header_type = nil
      expect(user_leader_photo).not_to be_valid
      expect(user_leader_photo.errors[:header_type]).to include("can't be blank")
    end

    it 'validates presence of priority' do
      user_leader_photo.priority = nil
      expect(user_leader_photo).not_to be_valid
      expect(user_leader_photo.errors[:priority]).to include("can't be blank")
    end
  end

  describe 'enums' do
    it 'defines header_type enum with correct values' do
      expect(UserLeaderPhoto.header_types).to eq({ 'header_1' => 0, 'header_2' => 1 })
    end

    it 'has header_type suffix methods' do
      user_leader_photo = build(:user_leader_photo, header_type: :header_1)
      expect(user_leader_photo.header_1_header_type?).to be true
      expect(user_leader_photo.header_2_header_type?).to be false
    end
  end

  describe 'callbacks' do
    describe '#queue_protocol_generation' do
      let(:user) { create(:user) }
      let(:circle) { create(:circle) }
      let(:user_poster_layout) { create(:user_poster_layout, entity: user) }
      let(:circle_poster_layout) { create(:user_poster_layout, entity: circle) }

      # NOTE: Protocol generation callback is currently disabled
      # These tests verify the callback behavior when it's enabled

      context 'when callback is disabled (current state)' do
        it 'does not queue GenerateProtocolImageWorker on create' do
          expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

          create(:user_leader_photo, user_poster_layout: user_poster_layout)
        end

        it 'does not queue GenerateProtocolImageWorker on update' do
          user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

          expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

          user_leader_photo.update!(priority: 2)
        end

        it 'does not queue GenerateProtocolImageWorker on destroy' do
          user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

          expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

          user_leader_photo.destroy!
        end
      end

      # TODO: Enable these tests when protocol generation callback is activated
      context 'when callback is enabled (future state)', :skip do
        context 'when user_poster_layout entity is a User' do
          it 'queues GenerateProtocolImageWorker on create' do
            expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id)

            create(:user_leader_photo, user_poster_layout: user_poster_layout)
          end

          it 'queues GenerateProtocolImageWorker on update' do
            user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

            expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id)

            user_leader_photo.update!(priority: 2)
          end

          it 'queues GenerateProtocolImageWorker on destroy' do
            user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

            expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id)

            user_leader_photo.destroy!
          end
        end

        context 'when user_poster_layout entity is a Circle' do
          it 'does not queue GenerateProtocolImageWorker on create' do
            expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

            create(:user_leader_photo, user_poster_layout: circle_poster_layout)
          end

          it 'does not queue GenerateProtocolImageWorker on update' do
            user_leader_photo = create(:user_leader_photo, user_poster_layout: circle_poster_layout)

            expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

            user_leader_photo.update!(priority: 2)
          end

          it 'does not queue GenerateProtocolImageWorker on destroy' do
            user_leader_photo = create(:user_leader_photo, user_poster_layout: circle_poster_layout)

            expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

            user_leader_photo.destroy!
          end
        end
      end
    end
  end

  describe 'protocol generation batching behavior' do
    let(:user) { create(:user) }
    let(:user_poster_layout) { create(:user_poster_layout, entity: user) }

    # NOTE: No setup needed since callback is disabled

    # NOTE: These tests are currently disabled since the callback is commented out
    # TODO: Enable these tests when protocol generation callback is activated

    context 'when callback is disabled (current state)' do
      it 'does not trigger any protocol generation jobs' do
        expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

        # Create multiple leader photos rapidly
        3.times do |i|
          create(:user_leader_photo,
                 user_poster_layout: user_poster_layout,
                 header_type: :header_1,
                 priority: i + 1)
        end
      end
    end

    context 'when callback is enabled (future state)', :skip do
      it 'batches multiple rapid changes within the deduplication window' do
        # Allow the worker to be called but don't actually execute
        allow(GenerateProtocolImageWorker).to receive(:perform_in)

        # Create multiple leader photos rapidly
        3.times do |i|
          create(:user_leader_photo,
                 user_poster_layout: user_poster_layout,
                 header_type: :header_1,
                 priority: i + 1)
        end

        # Should have been called 3 times (once for each create)
        expect(GenerateProtocolImageWorker).to have_received(:perform_in)
          .with(5.seconds, user.id).exactly(3).times
      end

      it 'handles updates to existing leader photos' do
        user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

        expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id)

        user_leader_photo.update!(header_type: :header_2)
      end

      it 'handles deletion of leader photos' do
        user_leader_photo = create(:user_leader_photo, user_poster_layout: user_poster_layout)

        expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id)

        user_leader_photo.destroy!
      end
    end
  end

  describe 'edge cases' do
    let(:user) { create(:user) }
    let(:user_poster_layout) { create(:user_poster_layout, entity: user) }

    context 'when callback is disabled (current state)' do
      it 'works with different header types without triggering jobs' do
        expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

        create(:user_leader_photo, user_poster_layout: user_poster_layout, header_type: :header_1)
        create(:user_leader_photo, user_poster_layout: user_poster_layout, header_type: :header_2)
      end

      it 'works with different priorities without triggering jobs' do
        expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

        create(:user_leader_photo, user_poster_layout: user_poster_layout, priority: 1)
        create(:user_leader_photo, user_poster_layout: user_poster_layout, priority: 2)
      end

      it 'handles user_poster_layout with different entity types' do
        circle = create(:circle)
        circle_layout = create(:user_poster_layout, entity: circle)

        # Should not queue worker for any entity type when callback is disabled
        expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

        create(:user_leader_photo, user_poster_layout: circle_layout)
      end
    end

    # TODO: Enable these tests when protocol generation callback is activated
    context 'when callback is enabled (future state)', :skip do
      it 'works with different header types' do
        expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id).twice

        create(:user_leader_photo, user_poster_layout: user_poster_layout, header_type: :header_1)
        create(:user_leader_photo, user_poster_layout: user_poster_layout, header_type: :header_2)
      end

      it 'works with different priorities' do
        expect(GenerateProtocolImageWorker).to receive(:perform_in).with(5.seconds, user.id).twice

        create(:user_leader_photo, user_poster_layout: user_poster_layout, priority: 1)
        create(:user_leader_photo, user_poster_layout: user_poster_layout, priority: 2)
      end

      it 'handles user_poster_layout with different entity types' do
        circle = create(:circle)
        circle_layout = create(:user_poster_layout, entity: circle)

        # Should not queue worker for circle entities
        expect(GenerateProtocolImageWorker).not_to receive(:perform_in)

        create(:user_leader_photo, user_poster_layout: circle_layout)
      end
    end
  end
end
